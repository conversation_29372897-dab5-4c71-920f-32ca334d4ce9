import Image from "next/image";
import { useCallback, useEffect, useState } from "react";
import { GameResultButton } from "@/app/components/buttons/game-result-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { Mask } from "@/app/components/mask";
import { Recaptcha } from "@/app/components/recaptcha";
import { GameId, GAME_NAMES } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { DrawResultDialog } from "./draw-result-dialog";
import { DrawResult } from "@prisma/client";
import { DialogButton } from "@/app/components/buttons/dialog-button";
import { AllCompleteDialog } from "./all-complete-dialog";
import { useCheckGameCompletion } from "../../../hooks/use-check-game-completion";
import { ctaClickEvent } from "@/utils/ga-event";

export const GameResult = ({
  gameTitle,
  nickname,
  gameId,
  score,
  gameRecordId,
  onPlayAgain,
}: {
  gameTitle: React.ReactNode;
  nickname: string;
  gameId: GameId;
  score: number;
  gameRecordId?: string;
  onPlayAgain: () => void;
}) => {
  const [drawResult, setDrawResult] = useState<DrawResult | undefined>(
    undefined,
  );
  const [couponCode, setCouponCode] = useState<string>("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [showRecaptcha, setShowRecaptcha] = useState(false);

  const { allGamesComplete: hasUserCompletedGames, hasCompletePrizeForm } =
    useCheckGameCompletion() ?? {};

  const getEventPrefix = useCallback(() => {
    switch (gameId) {
      case "balance":
        return "第一關";
      case "catch":
        return "第二關";
      case "quiz":
        return "第三關";
      default:
    }
  }, [gameId]);

  const handleDrawClick = () => {
    ctaClickEvent({ label: `${getEventPrefix()}_分享抽` });
    if (!gameRecordId) {
      alert("無法進行抽獎，請重新遊戲");
      return;
    }
    const shareLink = process.env.NEXT_PUBLIC_FB_SHARE_LINK;
    if (shareLink) {
      window.open(shareLink, "_blank");
    }

    // Show reCAPTCHA when user clicks draw button
    setShowRecaptcha(true);
  };

  const performDraw = async (recaptchaToken: string) => {
    if (!gameRecordId) {
      alert("無法進行抽獎，請重新遊戲");
      return;
    }

    if (!recaptchaToken) {
      alert("請完成人機驗證");
      return;
    }

    setIsDrawing(true);
    try {
      const response = await fetch("/api/game/draw", {
        method: "POST",
        body: JSON.stringify({
          gameRecordId,
          recaptchaToken,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setDrawResult(
          result.result === "win" ? DrawResult.WIN : DrawResult.LOSE,
        );
        if (result.coupon) {
          setCouponCode(result.coupon.code);
        }
      } else {
        const error = await response.json();
        alert(error.message || "抽獎失敗");
      }
    } catch (error) {
      console.error("Draw error:", error);
      alert("網絡錯誤，請稍後再試");
    } finally {
      setIsDrawing(false);
      setShowRecaptcha(false);
    }
  };

  const handleRecaptchaVerify = (token: string | null) => {
    if (token) {
      // Automatically perform draw when reCAPTCHA is verified
      performDraw(token);
    }
  };

  useEffect(() => {
    const headerLogo = document.getElementById("headerLogo");
    if (headerLogo) {
      const eventHandler = () => {
        const eventLabel = `${getEventPrefix()}_回首頁`;
        ctaClickEvent({ label: eventLabel });
      };
      headerLogo.addEventListener("click", eventHandler);
      return () => {
        headerLogo.removeEventListener("click", eventHandler);
      };
    }
  }, [getEventPrefix]);

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <Mask visible />
        <div className="z-0 w-[73vw] h-[23.5vw] flex justify-center items-center">
          {gameTitle}
        </div>
        <div className="relative z-0 mt-[5vw]">
          <Image
            unoptimized
            priority
            className="w-[67.5vw]"
            alt=""
            src={imageUrl("/game-result-background.png")}
            width={728}
            height={1001}
          />
          <div className="absolute w-full h-full left-0 top-0 flex flex-col py-[16.5vw] px-[10vw] font-[700]">
            <div>
              <span className="text-[8vw] text-[#fff100] font-[1000] mr-[3vw] overflow-hidden">
                {nickname}
              </span>
              <span className="text-[5vw]">您在</span>
            </div>
            <div className="mt-[3vw]">
              <span className="text-[5vw]">
                {
                  {
                    balance: `${GAME_NAMES.balance} 中堅持了`,
                    catch: `${GAME_NAMES.catch} 中獲得`,
                    quiz: `${GAME_NAMES.quiz} 中拿下`,
                  }[gameId]
                }
              </span>
            </div>
            <div className="text-[#fff100] font-[1000] mt-[2vw]">
              <span className="text-[14vw]">{score}</span>
              <span className="text-[12vw] relative bottom-[0.5vw]">
                {
                  {
                    balance: "秒",
                    catch: "分",
                    quiz: "分",
                  }[gameId]
                }
              </span>
            </div>
            <div>
              <span className="text-[5vw]">
                {
                  {
                    balance: score > 10 ? "實在太威啦!" : "再次挑戰極限吧！",
                    catch: "真的有夠威!",
                    quiz: score > 40 ? "簡直強氣炸裂!" : "展現態度再戰一局！",
                  }[gameId]
                }
              </span>
            </div>
          </div>
          <div className="flex gap-[2vw] justify-center mt-[2vw]">
            <GameResultButton
              onClick={handleDrawClick}
              disabled={isDrawing || showRecaptcha}
            >
              {isDrawing
                ? "抽獎中..."
                : showRecaptcha
                  ? "請完成驗證"
                  : "分享立即抽"}
            </GameResultButton>
            <GameResultButton
              onClick={() => {
                ctaClickEvent({ label: `${getEventPrefix()}_再次挑戰` });
                onPlayAgain();
              }}
            >
              再次挑戰
            </GameResultButton>
          </div>

          {/* reCAPTCHA Component */}
          {showRecaptcha && (
            <div className="fixed inset-0 w-full aspect-9/16 bg-black/90 flex flex-col items-center justify-center z-50 gap-[5vw]">
              <h3 className="text-[4vw] font-bold text-center">
                請完成人機驗證
              </h3>
              <div className="h-[20vw]">
                <Recaptcha
                  onVerify={handleRecaptchaVerify}
                  size="normal"
                  theme="light"
                />
              </div>

              <DialogButton onClick={() => setShowRecaptcha(false)}>
                取消
              </DialogButton>
            </div>
          )}
        </div>
      </div>
      {drawResult && (
        <DrawResultDialog
          onClose={() => setDrawResult(undefined)}
          onPlayAgain={onPlayAgain}
          drawResult={drawResult}
          code={couponCode || ""}
        />
      )}
      {hasUserCompletedGames && !hasCompletePrizeForm && <AllCompleteDialog />}
    </>
  );
};
