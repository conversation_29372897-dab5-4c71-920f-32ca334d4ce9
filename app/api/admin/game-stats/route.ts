import { GAME_NAMES, GameId } from "@/app/constants";
import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    // Get unique user counts for each game from PlayCount
    // Using groupBy with _count gives us the count of records, which equals unique users
    // since PlayCount has one record per user per game
    const gameStats = await prisma.playCount.groupBy({
      by: ["gameId"],
      _count: {
        userId: true,
      },
    });

    // Format the results
    const formattedStats = gameStats.map((stat) => ({
      gameId: stat.gameId,
      gameName: GAME_NAMES[stat.gameId as GameId] || stat.gameId,
      userCount: stat._count.userId,
    }));

    // Ensure all games are represented, even if no one has played them
    const allGames = ["balance", "catch", "quiz"];
    const completeStats = allGames.map((gameId) => {
      const existingStat = formattedStats.find(
        (stat) => stat.gameId === gameId,
      );
      return (
        existingStat || {
          gameId,
          gameName: GAME_NAMES[gameId as GameId],
          userCount: 0,
        }
      );
    });

    return NextResponse.json(completeStats);
  } catch (error) {
    console.error("Error fetching game stats:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
