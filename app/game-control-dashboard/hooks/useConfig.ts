import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { message } from "antd";
import {
  configService,
  type UpdateConfigRequest,
} from "../services/configService";

// Query Keys
export const configKeys = {
  all: ["config"] as const,
  websiteConfig: () => [...configKeys.all, "website"] as const,
  drawStats: () => [...configKeys.all, "stats"] as const,
  gameStats: () => [...configKeys.all, "gameStats"] as const,
};

// 獲取網站配置的 hook
export const useWebsiteConfig = () => {
  return useQuery({
    queryKey: configKeys.websiteConfig(),
    queryFn: () => configService.getWebsiteConfig(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// 獲取統計數據的 hook
export const useDrawStats = () => {
  return useQuery({
    queryKey: configKeys.drawStats(),
    queryFn: () => configService.getDrawStats(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
  });
};

// 獲取遊戲統計數據的 hook
export const useGameStats = () => {
  return useQuery({
    queryKey: configKeys.gameStats(),
    queryFn: () => configService.getGameStats(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// 更新網站配置的 mutation hook
export const useUpdateWebsiteConfig = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateConfigRequest) =>
      configService.updateWebsiteConfig(data),
    onSuccess: () => {
      message.success("配置更新成功");
      // 使配置和統計查詢失效，觸發重新獲取
      queryClient.invalidateQueries({
        queryKey: configKeys.websiteConfig(),
      });
      queryClient.invalidateQueries({
        queryKey: configKeys.drawStats(),
      });
      queryClient.invalidateQueries({
        queryKey: configKeys.gameStats(),
      });
    },
    onError: (error: Error) => {
      message.error(error.message);
    },
  });
};

// 手動重新獲取配置數據的 hook
export const useRefreshConfig = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: configKeys.all,
    });
  };
};
