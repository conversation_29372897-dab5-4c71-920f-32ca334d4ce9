interface WebsiteConfig {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  luckyDrawChance: number;
  revealDate?: string;
  prizeOwnerName?: string;
  prizeOwnerPhone?: string;
  createdAt: string;
  updatedAt: string;
}

interface ConfigStats {
  totalUsers: number;
  totalDraws: number;
  totalWins: number;
  actualWinRate: number;
}

interface GameStats {
  gameId: string;
  gameName: string;
  userCount: number;
}

interface UpdateConfigRequest {
  name: string;
  startDate: string;
  endDate: string;
  luckyDrawChance: number;
  revealDate?: string;
  prizeOwnerName?: string;
  prizeOwnerPhone?: string;
}

interface UpdateConfigResponse {
  message: string;
  config: WebsiteConfig;
}

export const configService = {
  // 獲取網站配置
  getWebsiteConfig: async (): Promise<WebsiteConfig> => {
    const response = await fetch("/api/admin/website-config");

    if (!response.ok) {
      throw new Error("獲取配置失敗");
    }

    return response.json();
  },

  // 獲取統計數據
  getDrawStats: async (): Promise<ConfigStats> => {
    const response = await fetch("/api/admin/draw-stats");

    if (!response.ok) {
      throw new Error("獲取統計數據失敗");
    }

    return response.json();
  },

  // 獲取遊戲統計數據
  getGameStats: async (): Promise<GameStats[]> => {
    const response = await fetch("/api/admin/game-stats");

    if (!response.ok) {
      throw new Error("獲取遊戲統計數據失敗");
    }

    return response.json();
  },

  // 更新網站配置
  updateWebsiteConfig: async (
    data: UpdateConfigRequest,
  ): Promise<UpdateConfigResponse> => {
    const response = await fetch("/api/admin/website-config", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "配置更新失敗");
    }

    return response.json();
  },
};

export type {
  WebsiteConfig,
  ConfigStats,
  UpdateConfigRequest,
  UpdateConfigResponse,
  GameStats,
};
