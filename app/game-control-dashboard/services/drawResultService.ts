interface DrawRecord {
  id: string;
  userId: string;
  gameId: string;
  score: number;
  result: "win" | "lose";
  createdAt: string;
  user?: {
    id?: string;
    name?: string;
    nickname?: string;
    email?: string;
  };
  coupon?: {
    code: string;
  };
}

interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface DrawResultsResponse {
  data: DrawRecord[];
  pagination: PaginationInfo;
}

interface DrawResultsParams {
  page?: number;
  pageSize?: number;
  startDate: string; // YYYY-MM-DD format
  endDate: string; // YYYY-MM-DD format
  gameId?: string;
  result?: string;
  search?: string;
}

export const drawResultService = {
  // 獲取抽獎結果列表
  getDrawResults: async (
    params: DrawResultsParams,
  ): Promise<DrawResultsResponse> => {
    const searchParams = new URLSearchParams();

    searchParams.set("startDate", params.startDate);
    searchParams.set("endDate", params.endDate);
    if (params.page) searchParams.set("page", params.page.toString());
    if (params.pageSize)
      searchParams.set("pageSize", params.pageSize.toString());
    if (params.gameId) searchParams.set("gameId", params.gameId);
    if (params.result) searchParams.set("result", params.result);
    if (params.search) searchParams.set("search", params.search);

    const response = await fetch(`/api/admin/draw-records?${searchParams}`);

    if (!response.ok) {
      throw new Error("獲取抽獎記錄失敗");
    }

    return response.json();
  },
};

export type {
  DrawRecord,
  PaginationInfo,
  DrawResultsResponse,
  DrawResultsParams,
};
