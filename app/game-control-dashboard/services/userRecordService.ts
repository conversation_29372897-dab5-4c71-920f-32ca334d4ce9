interface UserRecord {
  id: string;
  name?: string;
  nickname?: string;
  email?: string;
  luckyDrawName?: string;
  luckyDrawAge?: number;
  luckyDrawAddress?: string;
  luckyDrawPhone?: string;
  createdAt: string;
  gameRecords: {
    gameId: string;
    score: number;
    createdAt: string;
  }[];
  coupons: {
    code: string;
    createdAt: string;
  }[];
}

interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface UserRecordsResponse {
  data: UserRecord[];
  pagination: PaginationInfo;
}

interface UserRecordsParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export const userRecordService = {
  // 獲取用戶記錄列表
  getUserRecords: async (
    params: UserRecordsParams = {},
  ): Promise<UserRecordsResponse> => {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.set("page", params.page.toString());
    if (params.pageSize)
      searchParams.set("pageSize", params.pageSize.toString());
    if (params.search) searchParams.set("search", params.search);

    const response = await fetch(`/api/admin/user-records?${searchParams}`);

    if (!response.ok) {
      throw new Error("獲取用戶記錄失敗");
    }

    return response.json();
  },
};

export type {
  UserRecord,
  PaginationInfo,
  UserRecordsResponse,
  UserRecordsParams,
};
