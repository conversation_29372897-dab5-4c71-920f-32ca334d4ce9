"use client";
import React, { useState, useMemo, useEffect } from "react";
import { Table, Button, Space, Input, Tag } from "antd";
import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useUserRecords, useRefreshUserRecords } from "../hooks/useUserRecords";
import type { UserRecord } from "../services/userRecordService";
import { GAME_NAMES } from "@/app/constants";

const { Search } = Input;

const columns: ColumnsType<UserRecord> = [
  {
    title: "註冊時間",
    dataIndex: "createdAt",
    key: "createdAt",
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
    // sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
  },
  {
    title: "帳號名",
    dataIndex: "name",
    key: "name",
    render: (name: string) => name || "未設定",
  },
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    render: (email: string) => email || "未設定",
  },
  {
    title: "抽獎姓名",
    dataIndex: "luckyDrawName",
    key: "luckyDrawName",
    render: (name: string) => name || "-",
  },
  {
    title: "年齡",
    dataIndex: "luckyDrawAge",
    key: "luckyDrawAge",
    render: (age: number) => age || "-",
  },
  {
    title: "聯絡電話",
    dataIndex: "luckyDrawPhone",
    key: "luckyDrawPhone",
    render: (phone: string) => phone || "-",
  },
  {
    title: "地址",
    dataIndex: "luckyDrawAddress",
    key: "luckyDrawAddress",
    render: (address: string) => address || "-",
    ellipsis: true,
  },
  {
    title: "遊戲次數",
    dataIndex: "gameRecords",
    key: "gameCount",
    render: (records: UserRecord["gameRecords"]) => {
      // Count games by type
      const gameCounts = records.reduce(
        (acc, record) => {
          acc[record.gameId] = (acc[record.gameId] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      return (
        <div className="space-y-1">
          {Object.entries(GAME_NAMES).map(([gameId, title]) => (
            <div key={gameId} className="text-xs text-nowrap">
              {title || gameId}: {gameCounts[gameId]}
            </div>
          ))}
          {records.length === 0 && <span className="text-gray-400">無</span>}
        </div>
      );
    },
  },
  {
    title: "獲得PIN碼數",
    dataIndex: "coupons",
    key: "couponCount",
    render: (coupons: UserRecord["coupons"]) => (
      <Tag color={coupons.length > 0 ? "green" : "default"}>
        {coupons.length}
      </Tag>
    ),
    // sorter: (a, b) => a.coupons.length - b.coupons.length,
  },
  {
    title: "資格狀態",
    key: "eligibility",
    render: (_, record) => {
      const hasAllGames = ["balance", "catch", "quiz"].every((gameId) =>
        record.gameRecords.some((gr) => gr.gameId === gameId),
      );
      return (
        <Tag color={hasAllGames ? "green" : "orange"}>
          {hasAllGames ? "符合抽獎資格" : "未完成全部遊戲"}
        </Tag>
      );
    },
  },
];

export const LuckyDrawRecordPage: React.FC = () => {
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchText]);

  // React Query for fetching user records
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      pageSize,
      search: debouncedSearchText,
    }),
    [currentPage, pageSize, debouncedSearchText],
  );

  const { data: userRecordsData, isLoading } = useUserRecords(queryParams);
  const refreshUserRecords = useRefreshUserRecords();

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const exportToCSV = () => {
    if (!userRecordsData?.data) return;

    const csvContent = [
      [
        "註冊時間",
        "ID",
        "帳號名",
        "Email",
        "抽獎姓名",
        "年齡",
        "聯絡電話",
        "地址",
        `${GAME_NAMES.balance}次數`,
        `${GAME_NAMES.catch}次數`,
        `${GAME_NAMES.quiz}次數`,
        "獲得PIN碼數",
        "資格狀態",
      ],
      ...userRecordsData.data.map((record: UserRecord) => {
        const hasAllGames = ["balance", "catch", "quiz"].every((gameId) =>
          record.gameRecords.some((gr) => gr.gameId === gameId),
        );

        // Count games by type
        const gameCounts = record.gameRecords.reduce(
          (acc, gameRecord) => {
            acc[gameRecord.gameId] = (acc[gameRecord.gameId] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>,
        );

        return [
          dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
          record.id,
          record.name,
          record.email || "-",
          record.luckyDrawName || "-",
          record.luckyDrawAge?.toString() || "-",
          record.luckyDrawPhone || "-",
          record.luckyDrawAddress || "-",
          (gameCounts.balance || 0).toString(),
          (gameCounts.catch || 0).toString(),
          (gameCounts.quiz || 0).toString(),
          record.coupons.length.toString(),
          hasAllGames ? "符合抽獎資格" : "未完成全部遊戲",
        ];
      }),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `user-records-${dayjs().format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-bold mb-4">查詢 PS5 活動登錄名單</h2>

      <Space className="mb-4">
        <Search
          placeholder="搜尋暱稱、Email、姓名或電話"
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          onSearch={handleSearch}
          style={{ width: 300 }}
        />
        <Button
          type="primary"
          onClick={() => {
            refreshUserRecords();
            setCurrentPage(1);
          }}
          loading={isLoading}
        >
          重新整理
        </Button>
        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={!userRecordsData?.data || userRecordsData.data.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      <Table
        columns={columns}
        dataSource={userRecordsData?.data || []}
        loading={isLoading}
        rowKey="id"
        scroll={{ x: 900 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: userRecordsData?.pagination.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
          onChange: (page, size) => {
            setCurrentPage(page);
            if (size !== pageSize) {
              setPageSize(size);
              setCurrentPage(1);
            }
          },
          onShowSizeChange: (_, size) => {
            setPageSize(size);
            setCurrentPage(1);
          },
        }}
      />
    </div>
  );
};
